{"tabs": {"text": "<PERSON><PERSON><PERSON>", "document": "<PERSON><PERSON><PERSON> l<PERSON>"}, "languages": {"vietnamese": "Việt", "english": "<PERSON><PERSON>", "japanese": "<PERSON><PERSON><PERSON><PERSON>", "chinese": "Trung", "detectLanguage": "<PERSON><PERSON><PERSON> hi<PERSON> ngôn ngữ"}, "translation": {"translating": "<PERSON><PERSON> d<PERSON>ch...", "cannotTranslate": "<PERSON><PERSON><PERSON><PERSON> thể dịch v<PERSON><PERSON> bản", "translationWillAppear": "<PERSON><PERSON><PERSON> dịch sẽ xuất hiện ở đây", "processingFile": "<PERSON><PERSON> lý file...", "splittingFile": "<PERSON><PERSON> chia nhỏ file để xử lý...", "translatingParts": "<PERSON><PERSON> d<PERSON>ch {{count}} phần của file", "translatedParts": "<PERSON><PERSON> dịch {{completed}}/{{total}} phần...", "partsCompleted": "{{processed}}/{{total}} phần đã hoàn thành ({{progress}}%)", "noPartsTranslated": "<PERSON><PERSON><PERSON><PERSON> có phần n<PERSON>o đ<PERSON><PERSON><PERSON> dịch thành công", "translationError": "<PERSON><PERSON> xảy ra lỗi khi dịch văn bản"}, "alerts": {"translatingWait": "<PERSON><PERSON> dịch. <PERSON><PERSON> lòng đợi hoàn thành trước khi đổi ngôn ngữ.", "fileProcessingWait": "<PERSON><PERSON> dịch file hiện tại. <PERSON><PERSON> lòng đợi hoàn thành tr<PERSON><PERSON><PERSON> khi upload file mới."}, "phonetic": {"hide": "Ẩn {{type}}", "show": "Hiện {{type}}", "pinyin": "<PERSON><PERSON><PERSON>", "romaji": "<PERSON><PERSON>", "phonetic": "Phonetic"}, "sidebar": {"translation": "<PERSON><PERSON><PERSON>", "aiAgents": "AI Agents", "openSidebar": "Mở sidebar", "closeSidebar": "Đóng sidebar", "searchAgent": "<PERSON><PERSON><PERSON>", "newChat": "Đoạn chat mới"}, "header": {"translation": "<PERSON><PERSON><PERSON>", "aiAgent": "AI Agent", "chatAI": "Chat AI"}, "welcome": {"title": "<PERSON><PERSON>o mừng đến v<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> một dịch vụ từ sidebar để bắt đầu:", "translationDesc": "<PERSON><PERSON><PERSON> văn bản và tài liệu gi<PERSON>a các ngôn ngữ", "aiAgentsDesc": "<PERSON><PERSON><PERSON> chuy<PERSON>n với các AI agent ch<PERSON><PERSON><PERSON>"}, "menu": {"rename": "<PERSON><PERSON><PERSON> tên", "delete": "Xóa", "deleteChat": "<PERSON><PERSON>a đoạn chat?"}, "search": {"searchAgent": "<PERSON><PERSON><PERSON>"}, "context": {"welcomeMessage": "<PERSON><PERSON> chào, tôi có thể giúp gì cho bạn?"}, "fileUpload": {"dragDrop": "Kéo và thả tài liệu của bạn vào đây", "selectFile": "<PERSON><PERSON><PERSON> file từ thiết bị", "processing": "<PERSON><PERSON> lý file...", "translating": "<PERSON><PERSON> d<PERSON>ch...", "support": "Hỗ trợ file .txt và .docx - File sẽ được dịch và tự động tải về", "uploadNew": "Upload file mới sẽ khả dụng sau khi hoàn thành", "newFile": "Dịch file mới", "downloadTranslated": "<PERSON><PERSON><PERSON> file đã dịch", "originalFile": "File gốc", "successMessage": "đã đ<PERSON><PERSON><PERSON> dịch thành công!", "dragToStart": "Kéo thả file vào bên trái để bắt đầu dịch", "onlySupport": "Chỉ hỗ trợ file .txt và .docx", "translatingWait": "<PERSON><PERSON> d<PERSON>ch file, vui lòng đợi...", "downloadFile": "<PERSON><PERSON>i lại file đã dịch", "downloadTranslatedFile": "Tải file đã dịch ({{type}})", "combiningParts": "<PERSON><PERSON> gh<PERSON>p nối các phần đã dịch...", "creatingFile": "<PERSON><PERSON> tạo file đã dịch...", "errorCombining": "Lỗi khi ghép nối các phần đã dịch. <PERSON><PERSON> lòng thử lại.", "errorTranslating": "Lỗi khi dịch nội dung. <PERSON><PERSON> lòng thử lại.", "completed": "Hoàn thành! File đã sẵn sàng để tải về.", "errorProcessing": "Lỗi khi xử lý file. <PERSON><PERSON> lòng thử lại.", "noPartsTranslated": "<PERSON><PERSON><PERSON><PERSON> có phần n<PERSON>o đ<PERSON><PERSON><PERSON> dịch thành công"}, "textInput": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> văn bản cần d<PERSON>ch..."}, "chatbot": {"selectAgent": "<PERSON><PERSON> lòng chọn một AI Agent để bắt đầu trò chuyện", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải AI Agent. <PERSON><PERSON> lòng thử lại.", "inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> câu hỏi của bạn"}}