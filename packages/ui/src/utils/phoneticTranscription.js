import pinyinLib from 'pinyin'
import * as kuromoji from '@patdx/kuromoji'

// Use the correct pinyin function
const pinyin = pinyinLib.default || pinyinLib

// Initialize Kuromoji tokenizer instance
let tokenizer = null
const initKuromoji = async () => {
  if (!tokenizer) {
    console.log('Initializing Kuromoji...')

    // Custom loader for browser environment
    const myLoader = {
      async loadArrayBuffer(url) {
        // Strip off .gz extension since we're using uncompressed files
        url = url.replace('.gz', '')
        const res = await fetch('https://cdn.jsdelivr.net/npm/@aiktb/kuromoji@1.0.2/dict/' + url)
        if (!res.ok) {
          throw new Error(`Failed to fetch ${url}, status: ${res.status}`)
        }
        return res.arrayBuffer()
      }
    }

    tokenizer = await new kuromoji.TokenizerBuilder({
      loader: myLoader
    }).build()

    console.log('Kuromoji initialized successfully!')
  }
  return tokenizer
}

/**
 * Convert Chinese text to Pinyin with tone marks
 * @param {string} text - Chinese text to convert
 * @returns {string} - Pinyin with tone marks
 */
export const chineseToPinyin = (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    // Remove non-Chinese characters for processing but keep them in result
    const chineseRegex = /[\u4e00-\u9fff]/g
    const matches = text.match(chineseRegex)

    if (!matches || matches.length === 0) {
      return ''
    }

    // Convert Chinese characters to Pinyin with tone marks
    const pinyinResult = pinyin(text, {
      style: pinyin.STYLE_TONE, // Use tone marks (ā, á, ǎ, à)
      heteronym: false, // Use most common pronunciation
      segment: true // Enable word segmentation
    })

    // Flatten and join the pinyin result
    return pinyinResult.map((item) => (Array.isArray(item) ? item[0] : item)).join(' ')
  } catch (error) {
    console.error('Error converting Chinese to Pinyin:', error)
    return ''
  }
}

/**
 * Convert Japanese text to Romaji using Kuromoji tokenizer
 * @param {string} text - Japanese text to convert
 * @returns {Promise<string>} - Romaji transcription using Hepburn system
 */
export const japaneseToRomaji = async (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    console.log('Converting Japanese text to romaji:', text)

    // Initialize Kuromoji if not already done
    const kuromojiInstance = await initKuromoji()

    // Tokenize the text
    const tokens = kuromojiInstance.tokenize(text)

    // Extract readings and convert to romaji
    const romajiParts = tokens.map((token) => {
      // Use reading if available, otherwise use surface form
      const reading = token.reading || token.surface_form
      // Convert katakana reading to romaji using simple conversion
      return katakanaToRomaji(reading)
    })

    const romajiResult = romajiParts.join(' ')
    console.log('🚀 ~ phoneticTranscription.js ~ japaneseToRomaji ~ romajiResult:', romajiResult)

    return romajiResult
  } catch (error) {
    console.error('Error converting Japanese to Romaji:', error)
    return ''
  }
}

/**
 * Get phonetic transcription based on language
 * @param {string} text - Text to convert
 * @param {string} language - Target language ('Trung' for Chinese, 'Nhật' for Japanese)
 * @returns {Promise<string>} - Phonetic transcription
 */
export const getPhoneticTranscription = async (text, language) => {
  try {
    if (!text || !language) {
      return ''
    }

    switch (language) {
      case 'Trung':
        return chineseToPinyin(text)
      case 'Nhật':
        return await japaneseToRomaji(text)
      default:
        return ''
    }
  } catch (error) {
    console.error('Error getting phonetic transcription:', error)
    return ''
  }
}

/**
 * Check if a language supports phonetic transcription
 * @param {string} language - Language to check
 * @returns {boolean} - Whether the language supports phonetic transcription
 */
export const supportsPhoneticTranscription = (language) => {
  return language === 'Trung' || language === 'Nhật'
}

/**
 * Format text with phonetic transcription for display
 * @param {string} originalText - Original translated text
 * @param {string} phoneticText - Phonetic transcription
 * @param {string} language - Target language
 * @returns {object} - Formatted display object
 */
export const formatPhoneticDisplay = (originalText, phoneticText, language) => {
  return {
    original: originalText,
    phonetic: phoneticText,
    language: language,
    hasPhonetic: !!phoneticText && phoneticText.trim().length > 0
  }
}
