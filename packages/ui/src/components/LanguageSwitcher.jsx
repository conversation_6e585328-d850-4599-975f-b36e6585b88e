import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Box, IconButton, Menu, MenuItem, Typography, Tooltip, ListItemIcon, ListItemText, Divider } from '@mui/material'
import { IconLanguage, IconCheck } from '@tabler/icons-react'
import PropTypes from 'prop-types'

const LanguageSwitcher = ({ sx = {} }) => {
  const { i18n, t } = useTranslation()
  const [anchorEl, setAnchorEl] = useState(null)
  const open = Boolean(anchorEl)

  const languages = [
    { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ]

  const currentLanguage = languages.find((lang) => lang.code === i18n.language) || languages[0]

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode)
    handleClose()
  }

  return (
    <Box sx={sx}>
      <Tooltip title='Change Language'>
        <IconButton
          onClick={handleClick}
          size='small'
          sx={{
            color: '#5f6368',
            '&:hover': {
              backgroundColor: 'rgba(95, 99, 104, 0.1)'
            }
          }}
        >
          {currentLanguage.flag}
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            minWidth: 200,
            mt: 1,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: '1px solid #e8eaed'
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant='caption' color='text.secondary' fontWeight={500}>
            Select Language
          </Typography>
        </Box>
        <Divider />

        {languages.map((language) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            selected={currentLanguage.code === language.code}
            sx={{
              py: 1.5,
              px: 2,
              '&.Mui-selected': {
                backgroundColor: 'rgba(26, 115, 232, 0.08)',
                '&:hover': {
                  backgroundColor: 'rgba(26, 115, 232, 0.12)'
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 36 }}>
              <Typography fontSize='18px' sx={{ mr: 1 }}>
                {language.flag}
              </Typography>
              {currentLanguage.code === language.code && <IconCheck size={16} color='#1a73e8' />}
            </ListItemIcon>
            <ListItemText>
              <Typography
                variant='body2'
                sx={{
                  fontWeight: currentLanguage.code === language.code ? 500 : 400,
                  color: currentLanguage.code === language.code ? '#1a73e8' : 'text.primary'
                }}
              >
                {language.name}
              </Typography>
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  )
}

LanguageSwitcher.propTypes = {
  sx: PropTypes.object
}

export default LanguageSwitcher
